import moment from "moment";
var currency = 'vnd'
if ($.fn.dataTable) {
    $.extend($.fn.dataTable.defaults, {
        fnDrawCallback: function () {
            showToolTip();
        },
        "language": {
            "sProcessing": "Đang xử lý...",
            "sLengthMenu": "Xem _MENU_ mục",
            "emptyTable": "Không tìm thấy dòng nào phù hợp",
            "sZeroRecords": "Không tìm thấy dòng nào phù hợp",
            "sInfo": "Đang xem _START_ đến _END_ trong tổng số _TOTAL_ mục",
            "sInfoEmpty": "Đang xem 0 đến 0 trong tổng số 0 mục",
            "sInfoFiltered": "(được lọc từ _MAX_ mục)",
            "sInfoPostFix": "",
            "sSearch": "Tìm:",
            "sUrl": "",
            "oPaginate": {
                "sFirst": "Đầu",
                "sPrevious": "Trước",
                "sNext": "Tiếp",
                "sLast": "Cuối"
            }
        }
    });
}

const formatMoney = (input, suffix = '', roundNumber = true) => {
    if (!suffix) suffix = '';
    if (suffix == 'd') suffix = '₫';

    if (currency === 'usd') {
        roundNumber = false;
        if (suffix === '₫') suffix = '$';
        if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'USD')
    } else if (currency === 'baht') {
        roundNumber = false;
        if (suffix === '₫') suffix = '฿';
        if (suffix.match(/vnd|VNĐ/i)) suffix = suffix.replace(/vnd|VNĐ/i, 'Baht')
    }

    var number = parseFloat(input);
    if (roundNumber) number = Math.floor(number);
    if (isNaN(number)) return input;

    if (currency === 'usd') number = round(number, 6);
    if (currency === 'baht') number = round(number, 4);

    // If float
    if (number && number.toString().includes('.')) {
        let parts = number.toString().split('.');
        return formatMoney(parts[0]) + '.' + parts[1] + suffix;
    }
    else {
        return number.toLocaleString() + suffix;
    }
}
export const components = {
    default: function (text) {
        return `<span class="text-success">${text}</span>`;
    },
    btn_edit: function (row, prefix = '', className = 'btn-edit') {
        return `<a href="${prefix}/${row.id}/edit" data-id="${row.id}" class="me-2 ${className}" title="Sửa">
                <i class="ri-edit-line text-primary"></i>
            </a>`
    },
    btn_delete: function (row, prefix = '', className = 'btn-delete') {
        return `<a href="javascript:void(0)" data-id="${row.id}" data-url="${prefix}/${row.id}" class="me-2 ${className}" title="Xóa">
                <i class="ri-delete-bin-2-line text-danger"></i>
            </a>`
    },
    badge_success: (text) => `<span class="badge badge-success">${text}</span>`,
    badge_primary: (text) => `<span class="badge badge-primary">${text}</span>`,
    badge_warning: (text) => `<span class="badge badge-warning">${text}</span>`,
    badge_danger: (text) => `<span class="badge badge-danger">${text}</span>`,

    text_copyable: function (text) {
        return `<span class="copy-on-click text-success" data-title="Sao chép"
              data-toggle="tooltip" data-content="${text}">
              <i class="ri-file-copy-line"></i>
              ${text}
            </span>`;
    },

    table: {
        id: function (data, typeT, full) {
            return `<span class="text-success text-bold id-${full.id}">${data}</span>`;
        },
        url: function (data, type, full) {
            const displayText = data.length > 30 ? data.substring(0, 28) + '...' : data;
            return `<a href="${data}" class="text-plain" target="_blank">${displayText}</a>`;
        },

        title: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        name: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        username: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        platform_name: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        category: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        content: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        description: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },
        notes: function (data) {
            return `<span class="text-plain">${data ? data : ''}</span>`;
        }, 
        order_code: function (data) {
            return `<span class="text-plain">${data}</span>`;
        },  
        admin_note: function (data, typeT, full) {
            return `<span class="text-plain admin-note-${full.id}">${data ? data : ''}</span>`;
        },
        note: function (data, typeT, full) {
            return `<div class="text-plain note-${full.id}">${data}</div>`;
        },
        key: function (data) {
            return `<span class="badge bg-purple">${data}</span>`;
        },
        count: function (data) {
            return `<span class="badge bg-purple">${data}</span>`;
        },
        text_success: function (data) {
            return `<span class="text-success">${data}</span>`;
        },
        text_primary: function (data) {
            return `<span class="text-primary">${data}</span>`;
        },
        text_warning: function (data) {
            return `<span class="text-warning">${data}</span>`;
        },
        text_danger: function (data) {
            return `<span class="text-danger">${data}</span>`;
        },
        text_info: function (data) {
            return `<span class="text-info">${data}</span>`;
        },
        time: function (data) {
            if (!data) return ' ';
            return `<span class="badge bg-success">${moment(data).format('HH:mm:ss DD/MM/YYYY')}</span>`;
        },
        status: function (data, typeT, full) {
            return getStatusHtml(full);
        },
        type: function (data, typeT, full) {
            return getTypeHtml(full);
        },
        balance: function (data) {
            return `<span class="text-danger text-bold">${formatMoney(data, "")}</span>`;
        },
        member_price: function (data) {
            return `<span class="badge bg-purple">${formatMoney(data, "")}</span>`;
        },
        collaborator_price: function (data) {
            return `<span class="badge bg-orange">${formatMoney(data, "")}</span>`;
        },
        agency_price: function (data) {
            return `<span class="badge bg-pink">${formatMoney(data, "")}</span>`;
        },
        distributor_price: function (data) {
            return `<span class="badge bg-primary">${formatMoney(data, "")}</span>`;
        },
        price: function (data, type, row) {
            if (!row) return '';
            return `<span class="badge badge-sm bg-success">${formatMoney(row.balance_before || 0)}</span> 
            <span>${row.math || '+'}</span> 
            <span class="badge badge-sm bg-pink">${formatMoney(row.amount || 0)}</span> 
            <span>=</span> 
            <span class="badge badge-sm bg-primary money-value">${formatMoney(row.balance_after || 0)}</span>`;
        },
        price_per: function (data) {
            return `<span class="badge bg-success">${formatMoney(data, "")}</span>`;
        },
        total_payment: function (data) {
            return `<span class="badge bg-pink">${formatMoney(data, "")}</span>`;
        },
        order_status: function (data, dataT, full) {
            return getOrderStatusHtml(full);
        },
        tenant: function (data) {
            return `<span class="badge bg-purple">${data}</span>`;
        },
    }
}
function getStatusHtml(row) {
    const status = row.status;
    const statusMap = {
        published: { class: 'bg-success', text: 'Đã xuất bản' },
        draft: { class: 'bg-secondary', text: 'Bản nháp' },
        pending: { class: 'bg-warning', text: 'Chờ xử lý' },
        active: { class: 'bg-success', text: 'Hoạt động' },
        slow: { class: 'bg-orange', text: 'Chậm' },
        paused: { class: 'bg-danger', text: 'Bảo trì' },
        stopped: { class: 'bg-orange', text: 'Ngừng nhận đơn' },
        completed: { class: 'bg-success', text: 'Hoàn thành' },
    };

    const statusConfig = statusMap[status] || { class: 'bg-info', text: status };
    return `<span class="badge ${statusConfig.class}">${statusConfig.text}</span>`;
}
function getOrderStatusHtml(row) {
    const orderStatus = row.order_status;
    const orderStatusMap = {
        Processing: { class: 'bg-purple', text: 'Đang xử lý' },
        Pending: { class: 'bg-warning', text: 'Chờ xử lý' },
        Completed: { class: 'bg-success', text: 'Hoàn thành' },
        Cancelled: { class: 'bg-danger', text: 'Đã hủy' },
        In_progress: { class: 'bg-primary', text: 'Đang chạy' },
        Refunded: { class: 'bg-pink', text: 'Hoàn tiền' },
        Waiting_cancel: { class: 'bg-warning', text: 'Đang chờ hủy' }
    };

    const orderStatusConfig = orderStatusMap[orderStatus] || { class: 'bg-info', text: orderStatus };
    return `<span class="badge ${orderStatusConfig.class}">${orderStatusConfig.text}</span>`;
}
function getTypeHtml(row) {
    const type = row.type;
    const typeMap = {
        plus_money: { class: 'bg-purple', text: 'Cộng tiền' },
        minus_money: { class: 'bg-warning', text: 'Trừ tiền' },
        payment: { class: 'bg-success', text: 'Nạp tiền' },
        deposit: { class: 'bg-purple', text: 'Mua dịch vụ' },
    };

    const typeConfig = typeMap[type] || { class: 'bg-info', text: type };
    return `<span class="badge ${typeConfig.class}">${typeConfig.text}</span>`;
}
export const makeColumn = (title, name, render = null, disableSort = false) => {
    const obj = {
        title: title,
        data: name,
        name: name
    };

    if (disableSort) {
        Object.assign(obj, { orderable: false, searchable: false });
    }

    if (!render) render = name;

    if (render) {
        if (typeof render === 'string') {
            if (render === 'random') {
                const values = ['text_success', 'text_primary', 'text_warning', 'text_danger', 'text_info'];
                const randomIndex = Math.floor(Math.random() * values.length);
                obj.render = components.table[values[randomIndex]];
            }
            else if (typeof components[render] !== "undefined") {
                obj.render = components[render];
            } else if (typeof components.table[render] !== "undefined") {
                obj.render = components.table[render];
            }
            else {
                obj.render = components.table.content;
            }
        } else {
            obj.render = render;
        }
    }

    return obj;
}

/**
 * Re mapping the request url of datatable
 * @param url
 * @param callbackData
 */

var ajaxUrl,
    datatableVip,
    datatableLog;

/**
 * Re mapping the request url of datatable
 * @param url
 * @param callbackData
 */
export const xAjax = (url, callbackData = null) => {
    if (!ajaxUrl) ajaxUrl = url;
    return {
        url: url,
        data: function (data) {
            try {
                let order = data.order[0];
                order.field = data.columns[order.column].data;
                data.order_by = order.field;
                data.order_dir = order.dir;
                data.keyword = data.search.value;
                data.order = [];
                data.columns = [];

                if (typeof callbackData == "function") callbackData(data);

                return data;
            } catch (e) {
                console.log(e);
                return data;
            }
        }
    }
}
export const showToolTip = () => {
    $('[data-toggle="tooltip"], .btn-icon').tooltip();
}

export const reloadTable = () => {
    if (datatableVip) datatableVip.ajax.reload(showToolTip, false);
    if (datatableLog) datatableLog.ajax.reload(showToolTip, false);
}
export const definedColumns = {
    stt: makeColumn('STT', 'id'),
    title: makeColumn('Tiêu đề', 'title'),
    name: makeColumn('Tên', 'name'),
    key: makeColumn('Key', 'key'),
    content: makeColumn('Nội dung', 'content'),
    created_at: makeColumn('Thời gian', 'created_at', 'time'),
    action: function (render) {
        return makeColumn('Hành Động', 'id', render, true);
    },
    status: makeColumn('Trạng thái', 'status', 'status'),
    platform_name: makeColumn('Nền tảng', 'platform_name', 'platform_name'),
    category: makeColumn('Danh mục', 'category', 'category'),
    balance: makeColumn('Số dư', 'balance', 'balance'),
    member_price: makeColumn('Giá thành viên', 'member_price', 'member_price'),
    collaborator_price: makeColumn('Giá CTV', 'collaborator_price', 'collaborator_price'),
    agency_price: makeColumn('Giá Đại lý', 'agency_price', 'agency_price'),
    distributor_price: makeColumn('Giá nhà phân phối', 'distributor_price', 'distributor_price'),
    price: makeColumn('Số tiền', 'amount', 'price'),
    type: makeColumn('Kiểu', 'type', 'type'),
    username: makeColumn('Thành viên', 'username', 'username'),
    tenant: makeColumn('Website', 'tenant', 'tenant'),
    code: makeColumn('Code', 'code', 'code'),
    description: makeColumn('Mô tả', 'description', 'description'),
    count: makeColumn('Số lượng', 'count', 'count'),
    price_per: makeColumn('Giá', 'price_per', 'price_per'),
    total_payment: makeColumn('Tổng thanh toán', 'total_payment', 'total_payment'),
    order_status: makeColumn('Trạng thái', 'order_status', 'order_status'),
    notes: makeColumn('Ghi chú', 'notes', 'notes'),
    url: makeColumn('Đường dẫn', 'url', 'url'),
    note_editable: makeColumn('Ghi chú', 'note', function (note, t, order) {
        return `<div class="note-editable" data-id="${order.id}">${note}</div>`;
    }),
    admin_note: makeColumn('Admin ghi chú', 'admin_note', 'admin_note'),
    order_code: makeColumn('Mã đơn hàng', 'order_code', 'order_code'),
}
